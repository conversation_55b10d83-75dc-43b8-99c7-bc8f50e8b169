'use client';

import { useParams } from 'next/navigation';
import { FullPageLoader } from '@minicardiac-client/components';
import { useAuth } from '@minicardiac-client/apis';

import PostDetailLayout from '@/libs/components/src/lib/content-posts/detail-post/PostDetailLayout';
import { otherArticles } from '@/libs/components/src/lib/article-post/mockData';
import { QuestionAndAnswer } from '@/libs/components/src/lib/question-post/QuestionAndAnswer';
import { usePostById } from '@/libs/apis/src/lib/posts/use-feed';
import Head from '@/libs/components/src/lib/head/Head';

export default function QuestionPostPageWrapper() {
  const { authState } = useAuth();
  const params = useParams();

  const id = params?.id as string;

  const { data: post, isLoading } = usePostById(id);

  if (authState.isLoading || isLoading) {
    return <FullPageLoader open={true} />;
  }

  if (!post) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center', fontSize: '1.2rem' }}>
        Invalid post ID. No post found.
      </div>
    );
  }

  return (
    <>
      <Head title={post.title || post.content} />
      <PostDetailLayout
        user={{
          name: post.publisherName || '',
          profilePic: post.profileImageUrlThumbnail || '',
          postedAgo: post.postedAt || '',
        }}
        sidebarItems={otherArticles}
        sidebarTitle="Other Posts"
        allowPin
        postId={post.id ?? ''}
        post={post}
      >
        <QuestionAndAnswer
          pinnedAnswer={post.featuredComment ?? null}
          content={post.content ?? ''}
          postId={post.id ?? ''}
          tags={post.tags}
        />
      </PostDetailLayout>
    </>
  );
}
