'use client';
import { Box } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import React from 'react';

interface PostTagsProps {
  tags: string[];
  maxTags?: number;
}

const DEFAULT_LINE_HEIGHT = 18;

export const PostTags = ({ tags, maxTags = 9 }: PostTagsProps) => {
  const theme = useTheme();

  // Memoize tag parsing to avoid unnecessary recalculations when props haven't changed
  const tagList: string[] = React.useMemo(() =>
    tags?.length === 1 && typeof tags[0] === 'string' && tags[0].includes('#')
      ? tags[0]
          .split('#')
          .filter((t: string) => t.length > 0)
          .map((t: string) => t.trim())
      : tags?.map((tag) => tag.replace('#', '').trim()) || [],
    [tags]
  );

  if (!tags || tags.length === 0) return null;

  const visibleTags = tagList.slice(0, maxTags);
  const hiddenCount = tagList.length - maxTags;

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        flexWrap: 'wrap',
        width: '100%',
        minWidth: 0,
        borderRadius: '8px',
        padding: theme.spacing(1),
        gap: theme.spacing(0.125),
        background: theme.palette.grey[100],
        opacity: 1,
        overflow: 'hidden',
      }}
    >
      {visibleTags.map((tag: string, idx: number) => (
        <Box
          key={`${tag}-${idx}`}
          component="span"
          sx={{
            fontSize: '12px',
            color: theme.palette.text.primary,
            whiteSpace: 'nowrap',
            fontWeight: 600,
            fontFamily: theme.typography.fontFamily,
            background: 'none',
            borderRadius: 0,
            padding: 0,
            lineHeight: `${DEFAULT_LINE_HEIGHT}px`,
          }}
        >
          #{tag}
        </Box>
      ))}
      {hiddenCount > 0 && (
        <Box
          component="span"
          sx={{
            fontSize: '12px',
            color: theme.palette.secondary.main, // Secondary color for hidden tag count
            fontWeight: 600,
            fontFamily: theme.typography.fontFamily,
            marginLeft: theme.spacing(0.5),
            lineHeight: `${DEFAULT_LINE_HEIGHT}px`,
          }}
        >
          +{hiddenCount} more
        </Box>
      )}
    </Box>
  );
};